package com.faw.sa0214;

/**
 * 本地测试配置类
 * 专门用于本地开发和测试环境
 */
public class LocalTestConfig {
    
    /**
     * 本地测试源数据库配置
     */
    public static final DatabaseConfig.DatabaseInfo LOCAL_SOURCE = new DatabaseConfig.DatabaseInfo(
        "localhost",      // 本地主机
        3306,            // MySQL 默认端口
        "demo",       // 测试数据库名
        "root",    // CDC 用户
        "root", // 密码
        "mysql"          // 数据库类型
    );
    
    /**
     * 本地测试目标数据库配置
     */
    public static final DatabaseConfig.DatabaseInfo LOCAL_TARGET = new DatabaseConfig.DatabaseInfo(
        "localhost",      // 本地主机
        54321,            // 目标数据库端口 (避免冲突)
        "ep_rebate",       // 目标数据库名
        "root",        // 用户名
        "123456",       // 密码
        "kingbase"          // 临时使用 MySQL 模拟 Kingbase
    );
    
    /**
     * 本地测试表配置
     */
    public static final DatabaseConfig.TableConfig[] LOCAL_TABLE_CONFIGS = {
        new DatabaseConfig.TableConfig(LOCAL_SOURCE, new String[]{
            "demo",         // 用户表
            "team_manager"         // 订单表
        })
    };
    
    /**
     * 获取本地测试配置
     * 可以通过系统属性覆盖默认配置
     */
    public static DatabaseConfig.DatabaseInfo getLocalSourceConfig() {
        return LOCAL_SOURCE;
    }
    
    public static DatabaseConfig.DatabaseInfo getLocalTargetConfig() {
        return LOCAL_TARGET;
    }
}
