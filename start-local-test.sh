#!/bin/bash

# Flink CDC 本地测试启动脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查必要工具
check_prerequisites() {
    log_step "检查必要工具..."
    
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v mvn &> /dev/null; then
        log_error "Maven 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        log_warn "Docker 未安装，将跳过数据库容器启动"
        SKIP_DOCKER=true
    fi
    
    log_info "工具检查完成"
}

# 启动测试数据库
start_test_databases() {
    if [ "$SKIP_DOCKER" = true ]; then
        log_warn "跳过 Docker 数据库启动，请确保您已手动配置测试数据库"
        return
    fi
    
    log_step "启动测试数据库容器..."
    
    # 检查容器是否已存在
    if docker ps -a | grep -q "mysql-source"; then
        log_info "源数据库容器已存在，重启中..."
        docker start mysql-source || docker rm mysql-source
    fi
    
    if docker ps -a | grep -q "mysql-target"; then
        log_info "目标数据库容器已存在，重启中..."
        docker start mysql-target || docker rm mysql-target
    fi
    
    # 启动源数据库
    if ! docker ps | grep -q "mysql-source"; then
        log_info "启动源数据库 (端口 3306)..."
        docker run -d \
          --name mysql-source \
          -p 3306:3306 \
          -e MYSQL_ROOT_PASSWORD=123456 \
          -e MYSQL_DATABASE=test_db \
          -e MYSQL_USER=flink_user \
          -e MYSQL_PASSWORD=flink_password \
          mysql:8.0
    fi
    
    # 启动目标数据库
    if ! docker ps | grep -q "mysql-target"; then
        log_info "启动目标数据库 (端口 3307)..."
        docker run -d \
          --name mysql-target \
          -p 3307:3306 \
          -e MYSQL_ROOT_PASSWORD=123456 \
          -e MYSQL_DATABASE=tdsdata \
          -e MYSQL_USER=system \
          -e MYSQL_PASSWORD=King123 \
          mysql:8.0
    fi
    
    log_info "等待数据库启动..."
    sleep 30
    
    log_info "数据库容器启动完成"
}

# 初始化测试数据
init_test_data() {
    if [ "$SKIP_DOCKER" = true ]; then
        log_warn "跳过测试数据初始化，请手动执行 SQL 脚本"
        return
    fi
    
    log_step "初始化测试数据..."
    
    # 等待数据库完全启动
    log_info "等待数据库服务就绪..."
    for i in {1..30}; do
        if docker exec mysql-source mysql -uroot -p123456 -e "SELECT 1" &>/dev/null; then
            break
        fi
        sleep 2
    done
    
    # 创建测试数据
    log_info "创建源数据库测试数据..."
    docker exec -i mysql-source mysql -uroot -p123456 << 'EOF'
CREATE DATABASE IF NOT EXISTS test_db;
USE test_db;

CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    age INT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    order_no VARCHAR(50),
    amount DECIMAL(10,2),
    status INT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT IGNORE INTO users (id, name, email, age) VALUES 
(1, '张三', '<EMAIL>', 25),
(2, '李四', '<EMAIL>', 30),
(3, '王五', '<EMAIL>', 28);

INSERT IGNORE INTO orders (id, user_id, order_no, amount) VALUES 
(1, 1, 'ORD001', 100.50),
(2, 2, 'ORD002', 200.00),
(3, 1, 'ORD003', 150.75);

GRANT SELECT, RELOAD, SHOW DATABASES, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'flink_user'@'%';
FLUSH PRIVILEGES;
EOF

    log_info "创建目标数据库表结构..."
    docker exec -i mysql-target mysql -uroot -p123456 << 'EOF'
CREATE DATABASE IF NOT EXISTS tdsdata;
USE tdsdata;

CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    age INT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    order_no VARCHAR(50),
    amount DECIMAL(10,2),
    status INT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

GRANT ALL PRIVILEGES ON tdsdata.* TO 'system'@'%';
FLUSH PRIVILEGES;
EOF

    log_info "测试数据初始化完成"
}

# 编译项目
compile_project() {
    log_step "编译项目..."
    mvn clean compile -DskipTests
    log_info "项目编译完成"
}

# 启动测试作业
start_test_job() {
    log_step "启动 CDC 测试作业..."
    log_info "作业启动后，您可以在另一个终端执行数据库操作来测试同步功能"
    log_info "例如："
    log_info "  docker exec -it mysql-source mysql -uroot -p123456 test_db"
    log_info "  INSERT INTO users (name, email, age) VALUES ('新用户', '<EMAIL>', 35);"
    echo ""
    
    # 启动作业
    mvn exec:java -Dexec.mainClass="LocalTestJob"
}

# 清理环境
cleanup() {
    log_step "清理测试环境..."
    if [ "$SKIP_DOCKER" != true ]; then
        docker stop mysql-source mysql-target 2>/dev/null || true
        docker rm mysql-source mysql-target 2>/dev/null || true
    fi
    log_info "清理完成"
}

# 显示帮助
show_help() {
    echo "Flink CDC 本地测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start     - 启动完整测试环境 (默认)"
    echo "  compile   - 只编译项目"
    echo "  db        - 只启动数据库"
    echo "  run       - 只运行测试作业 (假设数据库已启动)"
    echo "  cleanup   - 清理测试环境"
    echo "  help      - 显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  SKIP_DOCKER=true  - 跳过 Docker 数据库启动"
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            check_prerequisites
            start_test_databases
            init_test_data
            compile_project
            start_test_job
            ;;
        compile)
            check_prerequisites
            compile_project
            ;;
        db)
            check_prerequisites
            start_test_databases
            init_test_data
            ;;
        run)
            check_prerequisites
            compile_project
            start_test_job
            ;;
        cleanup)
            cleanup
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 捕获 Ctrl+C 信号，清理环境
trap cleanup EXIT

# 执行主函数
main "$@"
