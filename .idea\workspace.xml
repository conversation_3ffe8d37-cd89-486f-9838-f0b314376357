<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c1f276d5-4a7c-449d-8197-a4d091444c7a" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:/tools/apache-maven-3.6.3" />
        <option name="localRepository" value="D:\maven\respository-faw" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\maven\gts-setting-latest.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="31mWUJQAx71emIZxcADchPcxVs0" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven. [org.apache.maven.plugins:maven-archetype-plugin:RELEASE:generate].executor": "Run",
    "Maven.flink-cdc-multi-source [clean].executor": "Run",
    "Maven.flink-cdc-multi-source-sync [clean].executor": "Run",
    "Maven.flink-cdc-multi-source-sync [compile].executor": "Run",
    "Maven.flink-cdc-multi-source-sync [install].executor": "Run",
    "Maven.flink-cdc-multi-source-sync [package].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "E:/workspace/workplace-self/flink_cdc_demo/lib",
    "project.structure.last.edited": "库",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.35172415",
    "settings.editor.selected.configurable": "MavenSettings",
    "应用程序.LocalTestJob.executor": "Run",
    "应用程序.com.faw.sa0214.LocalTestJob.executor": "Run"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\workspace\workplace-self\flink_cdc_demo\lib" />
    </key>
  </component>
  <component name="RunManager" selected="应用程序.com.faw.sa0214.LocalTestJob">
    <configuration name="LocalTestJob" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="LocalTestJob" />
      <module name="flink_cdc_demo" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="com.faw.sa0214.LocalTestJob" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.faw.sa0214.LocalTestJob" />
      <module name="flink_cdc_demo" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.faw.sa0214.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.com.faw.sa0214.LocalTestJob" />
        <item itemvalue="应用程序.LocalTestJob" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="c1f276d5-4a7c-449d-8197-a4d091444c7a" name="更改" comment="" />
      <created>1756129745192</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756129745192</updated>
    </task>
    <servers />
  </component>
</project>