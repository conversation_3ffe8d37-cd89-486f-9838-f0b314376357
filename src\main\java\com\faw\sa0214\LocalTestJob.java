package com.faw.sa0214;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import com.ververica.cdc.connectors.mysql.source.MySqlSource;
import com.ververica.cdc.connectors.mysql.table.StartupOptions;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.util.Properties;

/**
 * 本地测试作业
 * 用于在本地环境测试 CDC 功能
 */
@Slf4j
public class LocalTestJob {

    public static void main(String[] args) throws Exception {
        log.info("=== 开始本地 CDC 测试 ===");
        
        // 1. 配置本地测试环境
        log.info("配置本地测试环境...");
        DatabaseOperator.useLocalTestConfig();

        // 2. 测试数据库连接
        log.info("测试目标数据库连接...");
        if (!testTargetDatabase()) {
            log.error("目标数据库连接失败，请检查配置和数据库状态");
            return;
        }
        log.info("目标数据库连接成功！");
        
        // 2. 创建 Flink 执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 本地测试配置
        env.setParallelism(1); // 单并行度，便于调试
        env.enableCheckpointing(5000); // 5秒检查点间隔
        
        // 3. 创建 CDC 数据流
        createLocalCDCStream(env);
        
        // 4. 启动作业
        log.info("启动本地 CDC 测试作业...");
        log.info("请在源数据库中执行一些 INSERT/UPDATE/DELETE 操作来测试同步功能");
        env.execute("Local CDC Test Job");
    }
    
    /**
     * 测试目标数据库连接
     */
    private static boolean testTargetDatabase() {
        try {
            // 临时修改 DatabaseOperator 使用本地配置
            return DatabaseOperator.testConnection();
        } catch (Exception e) {
            log.error("数据库连接测试异常", e);
            return false;
        }
    }
    
    /**
     * 创建本地 CDC 数据流
     */
    private static void createLocalCDCStream(StreamExecutionEnvironment env) {
        DatabaseConfig.DatabaseInfo sourceDb = LocalTestConfig.getLocalSourceConfig();
        String[] tableNames = {"users", "orders"};
        
        try {
            // 配置 JDBC 属性
            Properties jdbcProps = new Properties();
            jdbcProps.setProperty("useSSL", "false");
            jdbcProps.setProperty("allowPublicKeyRetrieval", "true");
            jdbcProps.setProperty("serverTimezone", "Asia/Shanghai");
            
            // 构建完整表名
            String[] fullTableNames = new String[tableNames.length];
            for (int i = 0; i < tableNames.length; i++) {
                fullTableNames[i] = sourceDb.getDatabase() + "." + tableNames[i];
            }
            
            log.info("配置 CDC 源: {}:{}/{}", sourceDb.getHostname(), sourceDb.getPort(), sourceDb.getDatabase());
            log.info("监听表: {}", String.join(", ", fullTableNames));
            
            // 创建 MySQL CDC Source
            MySqlSource<String> mySqlSource = MySqlSource.<String>builder()
                    .hostname(sourceDb.getHostname())
                    .port(sourceDb.getPort())
                    .databaseList(sourceDb.getDatabase())
                    .tableList(fullTableNames)
                    .username(sourceDb.getUsername())
                    .password(sourceDb.getPassword())
                    .jdbcProperties(jdbcProps)
                    .startupOptions(StartupOptions.initial()) // 全量+增量
                    .deserializer(new JsonDebeziumDeserializationSchema())
                    .includeSchemaChanges(true)
                    .build();
            
            // 创建数据流
            DataStream<String> cdcStream = env.fromSource(
                mySqlSource, 
                WatermarkStrategy.noWatermarks(), 
                "Local CDC Source"
            );
            
            // 处理 CDC 事件
            cdcStream
                .filter(json -> json != null && !json.isEmpty())
                .map(new LocalCDCEventProcessor())
                .name("Local CDC Event Processor");
                
            log.info("CDC 数据流创建成功");
            
        } catch (Exception e) {
            log.error("创建 CDC 数据流失败", e);
            throw new RuntimeException("Failed to create local CDC stream", e);
        }
    }
    
    /**
     * 本地 CDC 事件处理器
     */
    public static class LocalCDCEventProcessor implements MapFunction<String, String> {
        
        private long eventCount = 0;
        private long lastLogTime = System.currentTimeMillis();
        
        @Override
        public String map(String jsonData) throws Exception {
            eventCount++;
            
            try {
                log.info("=== 接收到 CDC 事件 #{} ===", eventCount);
                log.info("原始数据: {}", jsonData);
                
                // 解析 CDC 事件
                JSONObject cdcEvent = JSON.parseObject(jsonData);
                
                // 提取基本信息
                String operation = cdcEvent.getString("op");
                JSONObject source = cdcEvent.getJSONObject("source");
                String database = source.getString("db");
                String table = source.getString("table");
                
                log.info("操作类型: {}, 数据库: {}, 表: {}", operation, database, table);
                
                // 根据操作类型处理
                switch (operation) {
                    case "c": // CREATE (INSERT)
                    case "r": // READ (初始快照)
                        handleInsert(cdcEvent, database, table);
                        break;
                    case "u": // UPDATE
                        handleUpdate(cdcEvent, database, table);
                        break;
                    case "d": // DELETE
                        handleDelete(cdcEvent, database, table);
                        break;
                    default:
                        log.warn("未知操作类型: {}", operation);
                }
                
                // 定期输出统计信息
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastLogTime > 10000) { // 每10秒
                    log.info("=== 统计信息 ===");
                    log.info("已处理事件总数: {}", eventCount);
                    lastLogTime = currentTime;
                }
                
                return jsonData;
                
            } catch (Exception e) {
                log.error("处理 CDC 事件失败: {}", jsonData, e);
                return null;
            }
        }
        
        private void handleInsert(JSONObject cdcEvent, String database, String table) {
            JSONObject after = cdcEvent.getJSONObject("after");
            log.info(">>> INSERT 操作");
            log.info("    表: {}.{}", database, table);
            log.info("    数据: {}", after);
            
            try {
                // 执行插入操作
                DatabaseOperator.executeInsert(table, after);
                log.info("    ✓ 插入成功");
            } catch (Exception e) {
                log.error("    ✗ 插入失败: {}", e.getMessage());
            }
        }
        
        private void handleUpdate(JSONObject cdcEvent, String database, String table) {
            JSONObject before = cdcEvent.getJSONObject("before");
            JSONObject after = cdcEvent.getJSONObject("after");
            log.info(">>> UPDATE 操作");
            log.info("    表: {}.{}", database, table);
            log.info("    变更前: {}", before);
            log.info("    变更后: {}", after);
            
            try {
                // 执行更新操作
                DatabaseOperator.executeUpdate(table, before, after);
                log.info("    ✓ 更新成功");
            } catch (Exception e) {
                log.error("    ✗ 更新失败: {}", e.getMessage());
            }
        }
        
        private void handleDelete(JSONObject cdcEvent, String database, String table) {
            JSONObject before = cdcEvent.getJSONObject("before");
            log.info(">>> DELETE 操作");
            log.info("    表: {}.{}", database, table);
            log.info("    删除数据: {}", before);
            
            try {
                // 执行删除操作
                DatabaseOperator.executeDelete(table, before);
                log.info("    ✓ 删除成功");
            } catch (Exception e) {
                log.error("    ✗ 删除失败: {}", e.getMessage());
            }
        }
    }
}
