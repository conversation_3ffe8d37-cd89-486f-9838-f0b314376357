# 本地测试指南

## 环境准备

### 1. 必要软件安装

```bash
# Java 17 (必须)
java -version

# Maven 3.6+ (必须)
mvn -version

# Docker (可选，用于数据库)
docker --version

# MySQL客户端 (可选，用于测试)
mysql --version
```

### 2. 数据库环境准备

#### 方案一：使用 Docker 快速搭建测试环境

```bash
# 启动 MySQL 作为源数据库
docker run -d \
  --name mysql-source \
  -p 3306:3306 \
  -e MYSQL_ROOT_PASSWORD=123456 \
  -e MYSQL_DATABASE=test_db \
  -e MYSQL_USER=flink_user \
  -e MYSQL_PASSWORD=flink_password \
  mysql:8.0

# 启动另一个 MySQL 作为目标数据库 (模拟 Kingbase)
docker run -d \
  --name mysql-target \
  -p 3307:3306 \
  -e MYSQL_ROOT_PASSWORD=123456 \
  -e MYSQL_DATABASE=tdsdata \
  -e MYSQL_USER=system \
  -e MYSQL_PASSWORD=King123 \
  mysql:8.0

# 等待数据库启动
sleep 30
```

#### 方案二：使用现有数据库

如果您有现有的数据库环境，请确保：
- 源数据库开启了 binlog
- 用户有相应的权限

### 3. 数据库配置

#### MySQL 源数据库配置

```sql
-- 连接到源数据库
mysql -h localhost -P 3306 -u root -p123456

-- 创建测试数据库和表
CREATE DATABASE IF NOT EXISTS test_db;
USE test_db;

-- 创建测试表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    age INT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    order_no VARCHAR(50),
    amount DECIMAL(10,2),
    status INT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入测试数据
INSERT INTO users (name, email, age) VALUES 
('张三', '<EMAIL>', 25),
('李四', '<EMAIL>', 30),
('王五', '<EMAIL>', 28);

INSERT INTO orders (user_id, order_no, amount) VALUES 
(1, 'ORD001', 100.50),
(2, 'ORD002', 200.00),
(1, 'ORD003', 150.75);

-- 创建 CDC 用户并授权
CREATE USER 'flink_user'@'%' IDENTIFIED BY 'flink_password';
GRANT SELECT, RELOAD, SHOW DATABASES, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'flink_user'@'%';
FLUSH PRIVILEGES;

-- 检查 binlog 是否开启
SHOW VARIABLES LIKE 'log_bin';
SHOW VARIABLES LIKE 'binlog_format';
```

#### 目标数据库配置

```sql
-- 连接到目标数据库
mysql -h localhost -P 3307 -u root -p123456

-- 创建目标数据库和表 (结构完全相同)
CREATE DATABASE IF NOT EXISTS tdsdata;
USE tdsdata;

-- 创建相同结构的表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    age INT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    order_no VARCHAR(50),
    amount DECIMAL(10,2),
    status INT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建目标数据库用户
CREATE USER 'system'@'%' IDENTIFIED BY 'King123';
GRANT ALL PRIVILEGES ON tdsdata.* TO 'system'@'%';
FLUSH PRIVILEGES;
```

## 项目配置

### 1. 修改数据库配置

编辑 `src/main/java/DatabaseConfig.java`：

```java
// 源数据库配置 (本地测试)
public static final DatabaseInfo TEST_SOURCE = new DatabaseInfo(
    "localhost",  // 本地主机
    3306,         // MySQL 端口
    "test_db",    // 测试数据库
    "flink_user", // CDC 用户
    "flink_password", // 密码
    "mysql"
);

// 目标数据库配置 (本地测试)
public static final DatabaseInfo TARGET_DATABASE = new DatabaseInfo(
    "localhost",  // 本地主机
    3307,         // 目标数据库端口
    "tdsdata",    // 目标数据库
    "system",     // 用户名
    "King123",    // 密码
    "mysql"       // 临时使用 MySQL 模拟 Kingbase
);

// 测试表配置
public static final TableConfig[] TABLE_CONFIGS = {
    new TableConfig(TEST_SOURCE, new String[]{
        "users",
        "orders"
    })
};
```

### 2. 创建本地测试类

创建 `src/main/java/LocalTestJob.java`：

```java
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

@Slf4j
public class LocalTestJob {
    
    public static void main(String[] args) throws Exception {
        log.info("开始本地测试...");
        
        // 测试数据库连接
        if (!DatabaseOperator.testConnection()) {
            log.error("数据库连接测试失败，请检查配置");
            return;
        }
        
        // 创建 Flink 环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1); // 本地测试使用单并行度
        env.enableCheckpointing(5000); // 5秒检查点
        
        // 创建 CDC 数据流
        for (DatabaseConfig.TableConfig tableConfig : DatabaseConfig.TABLE_CONFIGS) {
            SimplifiedCDCJob.createCDCDataStream(env, tableConfig);
        }
        
        log.info("启动本地测试作业...");
        env.execute("Local Test CDC Job");
    }
}
```

## 运行测试

### 1. 编译项目

```bash
# 清理并编译
mvn clean compile

# 如果有依赖问题，可以跳过测试
mvn clean compile -DskipTests
```

### 2. 运行测试

```bash
# 方式一：使用 Maven 运行
mvn exec:java -Dexec.mainClass="LocalTestJob"

# 方式二：使用 IDE 运行
# 在 IntelliJ IDEA 或 Eclipse 中直接运行 LocalTestJob.main()

# 方式三：打包后运行
mvn package -DskipTests
java -cp target/classes:target/lib/* LocalTestJob
```

### 3. 测试数据变更

在另一个终端中连接源数据库，执行一些变更操作：

```sql
-- 连接源数据库
mysql -h localhost -P 3306 -u root -p123456 test_db

-- 测试 INSERT
INSERT INTO users (name, email, age) VALUES ('测试用户', '<EMAIL>', 35);

-- 测试 UPDATE
UPDATE users SET age = 26 WHERE name = '张三';

-- 测试 DELETE
DELETE FROM orders WHERE order_no = 'ORD003';

-- 查看变更
SELECT * FROM users;
SELECT * FROM orders;
```

然后检查目标数据库是否同步了这些变更：

```sql
-- 连接目标数据库
mysql -h localhost -P 3307 -u root -p123456 tdsdata

-- 检查同步结果
SELECT * FROM users;
SELECT * FROM orders;
```

## 监控和调试

### 1. 查看日志

Flink 作业运行时会输出详细日志，关注以下信息：
- CDC 连接状态
- 数据变更事件
- 数据库操作结果
- 错误信息

### 2. 常见问题排查

#### 连接失败
```bash
# 检查数据库是否启动
docker ps

# 检查端口是否开放
netstat -an | grep 3306
netstat -an | grep 3307

# 测试数据库连接
mysql -h localhost -P 3306 -u flink_user -pflink_password test_db
mysql -h localhost -P 3307 -u system -pKing123 tdsdata
```

#### binlog 问题
```sql
-- 检查 binlog 配置
SHOW VARIABLES LIKE 'log_bin';
SHOW VARIABLES LIKE 'binlog_format';
SHOW VARIABLES LIKE 'binlog_row_image';

-- 如果 binlog 未开启，需要修改 MySQL 配置
-- 在 my.cnf 中添加：
-- [mysqld]
-- log-bin=mysql-bin
-- binlog-format=ROW
-- server-id=1
```

### 3. 性能监控

可以在代码中添加一些监控指标：

```java
// 在 CDCEventProcessor 中添加计数器
private static long processedEvents = 0;
private static long lastLogTime = System.currentTimeMillis();

// 在处理事件时更新计数
processedEvents++;
if (System.currentTimeMillis() - lastLogTime > 10000) { // 每10秒输出一次
    log.info("已处理事件数: {}", processedEvents);
    lastLogTime = System.currentTimeMillis();
}
```

## 清理环境

测试完成后，清理 Docker 容器：

```bash
# 停止并删除容器
docker stop mysql-source mysql-target
docker rm mysql-source mysql-target

# 清理数据卷 (可选)
docker volume prune
```
