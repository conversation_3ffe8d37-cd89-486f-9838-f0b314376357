package com.faw.sa0214;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import com.ververica.cdc.connectors.mysql.source.MySqlSource;
import com.ververica.cdc.connectors.mysql.table.StartupOptions;
import com.ververica.cdc.debezium.JsonDebeziumDeserializationSchema;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.util.Properties;

/**
 * 简化的 Flink CDC 多源同步作业
 * 基于 DataStream API，自动处理表结构，表名和字段完全一致
 */
@Slf4j
public class SimplifiedCDCJob {

    public static void main(String[] args) throws Exception {
        // 1. 初始化执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(2);
        env.disableOperatorChaining();
        env.enableCheckpointing(10_000); // 每10秒做一次Checkpoint

        // 2. 为每个源数据库创建CDC数据流
        for (DatabaseConfig.TableConfig tableConfig : DatabaseConfig.TABLE_CONFIGS) {
            createCDCDataStream(env, tableConfig);
        }

        // 3. 启动作业
        log.info("启动多源CDC同步作业...");
        env.execute("Simplified Multi-Source CDC Sync Job");
    }

    /**
     * 为源数据库创建CDC数据流
     */
    private static void createCDCDataStream(StreamExecutionEnvironment env, 
                                           DatabaseConfig.TableConfig tableConfig) {
        
        DatabaseConfig.DatabaseInfo sourceDb = tableConfig.getSourceDatabase();
        String[] tableNames = tableConfig.getTableNames();
        
        try {
            // 配置JDBC连接属性
            Properties jdbcProps = new Properties();
            jdbcProps.setProperty("useSSL", "false");
            jdbcProps.setProperty("allowPublicKeyRetrieval", "true");
            jdbcProps.setProperty("serverTimezone", "Asia/Shanghai");

            // 构建完整表名列表 (database.table格式)
            String[] fullTableNames = new String[tableNames.length];
            for (int i = 0; i < tableNames.length; i++) {
                fullTableNames[i] = sourceDb.getDatabase() + "." + tableNames[i];
            }

            // 创建MySQL CDC Source
            MySqlSource<String> mySqlSource = MySqlSource.<String>builder()
                    .hostname(sourceDb.getHostname())
                    .port(sourceDb.getPort())
                    .databaseList(sourceDb.getDatabase())
                    .tableList(fullTableNames)
                    .username(sourceDb.getUsername())
                    .password(sourceDb.getPassword())
                    .jdbcProperties(jdbcProps)
                    .startupOptions(StartupOptions.initial()) // 全量+增量同步
                    .deserializer(new JsonDebeziumDeserializationSchema())
                    .includeSchemaChanges(true)
                    .build();

            // 创建数据流并处理
            String sourceName = sourceDb.getDatabase() + " CDC Source";
            DataStream<String> cdcStream = env.fromSource(mySqlSource, WatermarkStrategy.noWatermarks(), sourceName);
            
            // 处理CDC事件
            cdcStream
                .filter(json -> json != null && !json.isEmpty())
                .map(new CDCEventProcessor())
                .name("Process " + sourceDb.getDatabase() + " Events")
                .setParallelism(1);

            log.info("成功创建CDC数据流: {} (包含 {} 个表)", sourceDb.getDatabase(), tableNames.length);

        } catch (Exception e) {
            log.error("创建CDC数据流失败: {}", sourceDb.getDatabase(), e);
        }
    }

    /**
     * CDC事件处理器
     */
    public static class CDCEventProcessor implements MapFunction<String, String> {
        
        @Override
        public String map(String jsonData) throws Exception {
            try {
                log.info("接收到CDC事件: {}", jsonData);
                
                // 解析CDC事件
                JSONObject cdcEvent = JSON.parseObject(jsonData);
                
                // 提取关键信息
                String operation = cdcEvent.getString("op"); // c=create, u=update, d=delete, r=read
                JSONObject source = cdcEvent.getJSONObject("source");
                String database = source.getString("db");
                String table = source.getString("table");
                
                // 根据操作类型处理数据
                switch (operation) {
                    case "c": // INSERT
                    case "r": // READ (初始快照)
                        handleInsert(cdcEvent, database, table);
                        break;
                    case "u": // UPDATE
                        handleUpdate(cdcEvent, database, table);
                        break;
                    case "d": // DELETE
                        handleDelete(cdcEvent, database, table);
                        break;
                    default:
                        log.warn("未知的操作类型: {}", operation);
                }
                
                return jsonData;
                
            } catch (Exception e) {
                log.error("处理CDC事件失败: {}", jsonData, e);
                // 可以选择记录错误或重试
                return null;
            }
        }
        
        private void handleInsert(JSONObject cdcEvent, String database, String table) {
            JSONObject after = cdcEvent.getJSONObject("after");
            log.info("处理INSERT事件: {}.{}, 数据: {}", database, table, after);

            try {
                // 直接插入到目标数据库的同名表
                DatabaseOperator.executeInsert(table, after);
            } catch (Exception e) {
                log.error("INSERT操作失败: {}.{}", database, table, e);
                // 可以选择重试或记录错误
            }
        }

        private void handleUpdate(JSONObject cdcEvent, String database, String table) {
            JSONObject before = cdcEvent.getJSONObject("before");
            JSONObject after = cdcEvent.getJSONObject("after");
            log.info("处理UPDATE事件: {}.{}, 变更前: {}, 变更后: {}", database, table, before, after);

            try {
                // 更新目标数据库的同名表
                DatabaseOperator.executeUpdate(table, before, after);
            } catch (Exception e) {
                log.error("UPDATE操作失败: {}.{}", database, table, e);
                // 可以选择重试或记录错误
            }
        }

        private void handleDelete(JSONObject cdcEvent, String database, String table) {
            JSONObject before = cdcEvent.getJSONObject("before");
            log.info("处理DELETE事件: {}.{}, 删除数据: {}", database, table, before);

            try {
                // 从目标数据库的同名表删除
                DatabaseOperator.executeDelete(table, before);
            } catch (Exception e) {
                log.error("DELETE操作失败: {}.{}", database, table, e);
                // 可以选择重试或记录错误
            }
        }
    }
}
