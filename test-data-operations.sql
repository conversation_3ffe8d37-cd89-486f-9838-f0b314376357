-- Flink CDC 本地测试数据操作脚本
-- 在 CDC 作业运行时执行这些操作来测试同步功能

-- 连接到源数据库：
-- docker exec -it mysql-source mysql -uroot -p123456 test_db

USE test_db;

-- 查看当前数据
SELECT '=== 当前用户数据 ===' AS info;
SELECT * FROM users;

SELECT '=== 当前订单数据 ===' AS info;
SELECT * FROM orders;

-- 测试 INSERT 操作
SELECT '=== 测试 INSERT 操作 ===' AS info;
INSERT INTO users (name, email, age) VALUES ('测试用户1', '<EMAIL>', 35);
INSERT INTO users (name, email, age) VALUES ('测试用户2', '<EMAIL>', 28);

INSERT INTO orders (user_id, order_no, amount) VALUES (1, 'ORD004', 299.99);
INSERT INTO orders (user_id, order_no, amount) VALUES (2, 'ORD005', 89.50);

-- 等待一下，让 CDC 处理
SELECT SLEEP(2);

-- 测试 UPDATE 操作
SELECT '=== 测试 UPDATE 操作 ===' AS info;
UPDATE users SET age = 26, email = '<EMAIL>' WHERE name = '张三';
UPDATE orders SET amount = 120.00, status = 2 WHERE order_no = 'ORD001';

-- 等待一下
SELECT SLEEP(2);

-- 测试 DELETE 操作
SELECT '=== 测试 DELETE 操作 ===' AS info;
DELETE FROM orders WHERE order_no = 'ORD003';
DELETE FROM users WHERE name = '王五';

-- 等待一下
SELECT SLEEP(2);

-- 查看最终数据
SELECT '=== 最终用户数据 ===' AS info;
SELECT * FROM users;

SELECT '=== 最终订单数据 ===' AS info;
SELECT * FROM orders;

-- 批量操作测试
SELECT '=== 批量操作测试 ===' AS info;
INSERT INTO users (name, email, age) VALUES 
('批量用户1', '<EMAIL>', 22),
('批量用户2', '<EMAIL>', 24),
('批量用户3', '<EMAIL>', 26);

UPDATE users SET age = age + 1 WHERE age < 30;

-- 检查目标数据库同步情况的脚本
-- 在另一个终端执行：
-- docker exec -it mysql-target mysql -uroot -p123456 tdsdata

/*
USE tdsdata;

SELECT '=== 目标数据库 - 用户表 ===' AS info;
SELECT * FROM users;

SELECT '=== 目标数据库 - 订单表 ===' AS info;
SELECT * FROM orders;

-- 比较记录数
SELECT 
    '用户表记录数' AS table_name,
    COUNT(*) AS record_count
FROM users
UNION ALL
SELECT 
    '订单表记录数' AS table_name,
    COUNT(*) AS record_count
FROM orders;
*/
